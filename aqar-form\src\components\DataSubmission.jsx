import { useState, useEffect } from 'react'
import SubmissionHistory from './SubmissionHistory'
import { sendToGoogleSheets, getGoogleSheetsUrl, testGoogleSheetsConnection } from '../services/googleSheetsService'
import './DataSubmission.css'

const DataSubmission = ({ onClose }) => {
  const [savedProperties, setSavedProperties] = useState([])
  const [selectedProperties, setSelectedProperties] = useState([])
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submissionHistory, setSubmissionHistory] = useState([])
  const [searchTerm, setSearchTerm] = useState('')
  const [showHistory, setShowHistory] = useState(false)
  const [submissionMethod, setSubmissionMethod] = useState('local') // 'local', 'googleSheets'
  const [connectionStatus, setConnectionStatus] = useState('unknown') // 'unknown', 'connected', 'failed'

  useEffect(() => {
    // تحميل العقارات المحفوظة
    const properties = JSON.parse(localStorage.getItem('saved-properties') || '[]')
    setSavedProperties(properties)

    // تحميل تاريخ الإرسال
    const history = JSON.parse(localStorage.getItem('submission-history') || '[]')
    setSubmissionHistory(history)

    // اختبار الاتصال مع Google Sheets
    testConnection()
  }, [])

  const testConnection = async () => {
    try {
      const isConnected = await testGoogleSheetsConnection()
      setConnectionStatus(isConnected ? 'connected' : 'failed')
    } catch (error) {
      setConnectionStatus('failed')
    }
  }

  const filteredProperties = savedProperties.filter(property => 
    !property.submitted && (
      property.unitCode?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      property.unitType?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      property.region?.some(r => r.toLowerCase().includes(searchTerm.toLowerCase()))
    )
  )

  const handleSelectProperty = (propertyIndex) => {
    const actualIndex = savedProperties.findIndex(p => p.unitCode === filteredProperties[propertyIndex].unitCode)
    
    if (selectedProperties.includes(actualIndex)) {
      setSelectedProperties(selectedProperties.filter(index => index !== actualIndex))
    } else {
      setSelectedProperties([...selectedProperties, actualIndex])
    }
  }

  const handleSelectAll = () => {
    if (selectedProperties.length === filteredProperties.length) {
      setSelectedProperties([])
    } else {
      const allIndexes = filteredProperties.map(property => 
        savedProperties.findIndex(p => p.unitCode === property.unitCode)
      )
      setSelectedProperties(allIndexes)
    }
  }

  const formatPropertyData = (property) => {
    return {
      unitCode: property.unitCode,
      unitType: property.unitType,
      unitCondition: Array.isArray(property.unitCondition) ? property.unitCondition.join(', ') : property.unitCondition,
      region: Array.isArray(property.region) ? property.region.join(', ') : property.region,
      floor: Array.isArray(property.floor) ? property.floor.join(', ') : property.floor,
      area: property.area,
      price: property.price,
      imageStatus: property.imageStatus,
      features: property.features,
      fullDetails: property.fullDetails,
      address: property.address,
      employeeName: property.employeeName,
      propertyAvailability: property.propertyAvailability,
      status: Array.isArray(property.status) ? property.status.join(', ') : property.status,
      createdAt: property.createdAt,
      lastProcessed: property.lastProcessed,
      reminderDate: property.reminderDate
    }
  }

  const handleSubmitData = async () => {
    if (selectedProperties.length === 0) {
      alert('يرجى اختيار عقار واحد على الأقل للإرسال')
      return
    }

    setIsSubmitting(true)

    try {
      const selectedData = selectedProperties.map(index => formatPropertyData(savedProperties[index]))

      let response
      let submissionType = submissionMethod

      if (submissionMethod === 'googleSheets') {
        // إرسال إلى Google Sheets
        console.log('إرسال البيانات إلى Google Sheets:', selectedData)
        response = await sendToGoogleSheets(selectedData)
        submissionType = 'Google Sheets'
      } else {
        // الطريقة المحلية (المحاكاة)
        console.log('حفظ البيانات محلياً:', selectedData)
        await new Promise(resolve => setTimeout(resolve, 2000))
        response = {
          success: true,
          message: `تم حفظ ${selectedData.length} عقار محلياً`
        }
        submissionType = 'محلي'
      }

      // تحديث حالة العقارات المرسلة
      const updatedProperties = [...savedProperties]
      selectedProperties.forEach(index => {
        updatedProperties[index] = {
          ...updatedProperties[index],
          submitted: true,
          submittedAt: new Date().toISOString()
        }
      })

      // حفظ العقارات المحدثة
      localStorage.setItem('saved-properties', JSON.stringify(updatedProperties))
      setSavedProperties(updatedProperties)

      // إضافة إلى تاريخ الإرسال
      const newSubmission = {
        id: Date.now(),
        date: new Date().toISOString(),
        propertiesCount: selectedProperties.length,
        properties: selectedData,
        status: response.success ? 'success' : 'failed',
        method: submissionType,
        message: response.message
      }

      const updatedHistory = [newSubmission, ...submissionHistory]
      localStorage.setItem('submission-history', JSON.stringify(updatedHistory))
      setSubmissionHistory(updatedHistory)

      // إعادة تعيين الاختيارات
      setSelectedProperties([])

      alert(`تم إرسال ${selectedProperties.length} عقار بنجاح إلى ${submissionType}!`)

    } catch (error) {
      console.error('خطأ في الإرسال:', error)

      // إضافة سجل فشل إلى التاريخ
      const failedSubmission = {
        id: Date.now(),
        date: new Date().toISOString(),
        propertiesCount: selectedProperties.length,
        properties: selectedProperties.map(index => formatPropertyData(savedProperties[index])),
        status: 'failed',
        method: submissionMethod === 'googleSheets' ? 'Google Sheets' : 'محلي',
        message: error.message
      }

      const updatedHistory = [failedSubmission, ...submissionHistory]
      localStorage.setItem('submission-history', JSON.stringify(updatedHistory))
      setSubmissionHistory(updatedHistory)

      alert(`حدث خطأ أثناء الإرسال إلى ${submissionMethod === 'googleSheets' ? 'Google Sheets' : 'النظام المحلي'}: ${error.message}`)
    } finally {
      setIsSubmitting(false)
    }
  }

  const exportSelectedData = () => {
    if (selectedProperties.length === 0) {
      alert('يرجى اختيار عقارات للتصدير')
      return
    }

    const selectedData = selectedProperties.map(index => formatPropertyData(savedProperties[index]))
    
    const headers = [
      'كود العقار', 'نوع الوحدة', 'حالة الوحدة', 'المنطقة', 'الدور',
      'المساحة', 'السعر', 'حالة الصور', 'المميزات', 'التفاصيل الكاملة',
      'العنوان', 'اسم الموظف', 'إتاحة العقار', 'الحالة', 'تاريخ الإنشاء'
    ]

    const csvContent = [
      headers.join(','),
      ...selectedData.map(property => [
        property.unitCode || '',
        property.unitType || '',
        property.unitCondition || '',
        property.region || '',
        property.floor || '',
        property.area || '',
        property.price || '',
        property.imageStatus || '',
        property.features || '',
        property.fullDetails || '',
        property.address || '',
        property.employeeName || '',
        property.propertyAvailability || '',
        property.status || '',
        property.createdAt ? new Date(property.createdAt).toLocaleDateString('ar-EG') : ''
      ].map(field => `"${field}"`).join(','))
    ].join('\n')

    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    link.href = URL.createObjectURL(blob)
    link.download = `عقارات_للإرسال_${new Date().toISOString().split('T')[0]}.csv`
    link.click()
  }

  return (
    <div className="data-submission-overlay">
      <div className="data-submission-modal">
        <div className="modal-header">
          <h2>إرسال البيانات الأصلية</h2>
          <button className="close-btn" onClick={onClose}>×</button>
        </div>

        <div className="submission-stats">
          <div className="stat-card">
            <span className="stat-number">{filteredProperties.length}</span>
            <span className="stat-label">عقار متاح للإرسال</span>
          </div>
          <div className="stat-card">
            <span className="stat-number">{selectedProperties.length}</span>
            <span className="stat-label">عقار محدد</span>
          </div>
          <div className="stat-card">
            <span className="stat-number">{submissionHistory.length}</span>
            <span className="stat-label">عملية إرسال سابقة</span>
          </div>
          <div className="stat-card">
            <span className={`stat-number ${connectionStatus === 'connected' ? 'success' : connectionStatus === 'failed' ? 'error' : ''}`}>
              {connectionStatus === 'connected' ? '✓' : connectionStatus === 'failed' ? '✗' : '?'}
            </span>
            <span className="stat-label">Google Sheets</span>
          </div>
        </div>

        <div className="submission-method">
          <h3>طريقة الإرسال:</h3>
          <div className="method-options">
            <label className={`method-option ${submissionMethod === 'local' ? 'active' : ''}`}>
              <input
                type="radio"
                name="submissionMethod"
                value="local"
                checked={submissionMethod === 'local'}
                onChange={(e) => setSubmissionMethod(e.target.value)}
              />
              <span className="method-icon">💾</span>
              <span className="method-text">حفظ محلي</span>
            </label>

            <label className={`method-option ${submissionMethod === 'googleSheets' ? 'active' : ''} ${connectionStatus === 'failed' ? 'disabled' : ''}`}>
              <input
                type="radio"
                name="submissionMethod"
                value="googleSheets"
                checked={submissionMethod === 'googleSheets'}
                onChange={(e) => setSubmissionMethod(e.target.value)}
                disabled={connectionStatus === 'failed'}
              />
              <span className="method-icon">📊</span>
              <span className="method-text">Google Sheets</span>
              {connectionStatus === 'failed' && <span className="method-status">غير متاح</span>}
            </label>
          </div>

          {submissionMethod === 'googleSheets' && connectionStatus === 'connected' && (
            <div className="sheets-info">
              <p>سيتم إرسال البيانات إلى Google Sheets مباشرة</p>
              <a href={getGoogleSheetsUrl()} target="_blank" rel="noopener noreferrer" className="sheets-link">
                🔗 عرض الجدول
              </a>
            </div>
          )}
        </div>

        <div className="submission-controls">
          <input
            type="text"
            placeholder="البحث في العقارات..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="search-input"
          />
          
          <div className="control-buttons">
            <button 
              className="btn btn-outline"
              onClick={handleSelectAll}
            >
              {selectedProperties.length === filteredProperties.length ? 'إلغاء تحديد الكل' : 'تحديد الكل'}
            </button>
            
            <button
              className="btn btn-secondary"
              onClick={exportSelectedData}
              disabled={selectedProperties.length === 0}
            >
              📊 تصدير المحدد
            </button>

            <button
              className="btn btn-info"
              onClick={() => setShowHistory(true)}
            >
              📋 تاريخ الإرسال
            </button>

            <button
              className="btn btn-primary"
              onClick={handleSubmitData}
              disabled={selectedProperties.length === 0 || isSubmitting}
            >
              {isSubmitting ? '⏳ جاري الإرسال...' : `📤 إرسال (${selectedProperties.length})`}
            </button>
          </div>
        </div>

        <div className="properties-grid">
          {filteredProperties.length === 0 ? (
            <div className="empty-state">
              {searchTerm ? 'لا توجد نتائج للبحث' : 'لا توجد عقارات متاحة للإرسال'}
            </div>
          ) : (
            filteredProperties.map((property, index) => {
              const actualIndex = savedProperties.findIndex(p => p.unitCode === property.unitCode)
              const isSelected = selectedProperties.includes(actualIndex)
              
              return (
                <div 
                  key={property.unitCode} 
                  className={`property-card ${isSelected ? 'selected' : ''}`}
                  onClick={() => handleSelectProperty(index)}
                >
                  <div className="property-header">
                    <div className="property-checkbox">
                      <input 
                        type="checkbox" 
                        checked={isSelected}
                        onChange={() => handleSelectProperty(index)}
                        onClick={(e) => e.stopPropagation()}
                      />
                    </div>
                    <h3>{property.unitCode}</h3>
                  </div>
                  
                  <div className="property-summary">
                    <div className="summary-row">
                      <span className="label">النوع:</span>
                      <span className="value">{property.unitType || 'غير محدد'}</span>
                    </div>
                    <div className="summary-row">
                      <span className="label">المنطقة:</span>
                      <span className="value">
                        {Array.isArray(property.region) 
                          ? property.region.slice(0, 2).join(', ') + (property.region.length > 2 ? '...' : '')
                          : property.region || 'غير محدد'
                        }
                      </span>
                    </div>
                    <div className="summary-row">
                      <span className="label">السعر:</span>
                      <span className="value">{property.price ? `${property.price} جنيه` : 'غير محدد'}</span>
                    </div>
                  </div>
                </div>
              )
            })
          )}
        </div>

        {showHistory && (
          <SubmissionHistory onClose={() => setShowHistory(false)} />
        )}
      </div>
    </div>
  )
}

export default DataSubmission
