import { useState, useEffect } from 'react'
import { formQuestions } from '../data/formQuestions'
import './ConversationalForm.css'

const ConversationalForm = ({ formData, setFormData, onSubmit }) => {
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0)
  const [answers, setAnswers] = useState([])
  const [isCompleted, setIsCompleted] = useState(false)

  const currentQuestion = formQuestions[currentQuestionIndex]
  const progress = ((currentQuestionIndex + 1) / formQuestions.length) * 100

  const handleAnswer = (answer) => {
    const newAnswers = [...answers]
    newAnswers[currentQuestionIndex] = answer

    setAnswers(newAnswers)

    // تحديث بيانات الفورم
    const updatedFormData = { ...formData }
    updatedFormData[currentQuestion.field] = answer
    setFormData(updatedFormData)

    // إضافة تأثير بصري للإجابة
    const selectedBtn = document.querySelector('.answer-btn:hover') ||
                       document.querySelector('.answer-btn:focus')
    if (selectedBtn) {
      selectedBtn.style.transform = 'scale(0.95)'
      setTimeout(() => {
        selectedBtn.style.transform = 'scale(1)'
      }, 150)
    }

    // الانتقال للسؤال التالي أو إنهاء الفورم
    if (currentQuestionIndex < formQuestions.length - 1) {
      setTimeout(() => {
        setCurrentQuestionIndex(currentQuestionIndex + 1)
      }, 600)
    } else {
      setTimeout(() => {
        setIsCompleted(true)
      }, 600)
    }
  }

  const handlePrevious = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(currentQuestionIndex - 1)
    }
  }

  const handleSubmit = () => {
    onSubmit(formData)
  }

  const renderAnswerOptions = () => {
    const question = currentQuestion

    if (question.type === 'select') {
      return (
        <div className="answer-options">
          {question.options.map((option, index) => (
            <button
              key={index}
              className="answer-btn"
              onClick={() => handleAnswer(option)}
            >
              {option}
            </button>
          ))}
        </div>
      )
    }

    if (question.type === 'multi-select') {
      const currentAnswer = answers[currentQuestionIndex] || []
      
      return (
        <div className="multi-select-container">
          <div className="answer-options">
            {question.options.map((option, index) => (
              <button
                key={index}
                className={`answer-btn ${currentAnswer.includes(option) ? 'selected' : ''}`}
                onClick={() => {
                  const newAnswer = currentAnswer.includes(option)
                    ? currentAnswer.filter(item => item !== option)
                    : [...currentAnswer, option]
                  
                  const newAnswers = [...answers]
                  newAnswers[currentQuestionIndex] = newAnswer
                  setAnswers(newAnswers)
                  
                  const updatedFormData = { ...formData }
                  updatedFormData[currentQuestion.field] = newAnswer
                  setFormData(updatedFormData)
                }}
              >
                {option}
              </button>
            ))}
          </div>
          <button 
            className="continue-btn"
            onClick={() => handleAnswer(answers[currentQuestionIndex] || [])}
            disabled={!answers[currentQuestionIndex] || answers[currentQuestionIndex].length === 0}
          >
            متابعة
          </button>
        </div>
      )
    }

    if (question.type === 'number' || question.type === 'text') {
      return (
        <div className="text-input-container">
          <input
            type={question.type}
            className="text-input"
            placeholder={question.placeholder || `أدخل ${question.question}`}
            onKeyPress={(e) => {
              if (e.key === 'Enter' && e.target.value.trim()) {
                handleAnswer(e.target.value.trim())
              }
            }}
            autoFocus
          />
          <button
            className="continue-btn"
            onClick={(e) => {
              const input = e.target.previousElementSibling
              if (input.value.trim()) {
                handleAnswer(input.value.trim())
              }
            }}
          >
            متابعة
          </button>
        </div>
      )
    }
  }

  if (isCompleted) {
    return (
      <div className="completion-screen">
        <div className="completion-content">
          <div className="success-icon">✅</div>
          <h2>تم إكمال البيانات!</h2>
          <p>يرجى مراجعة البيانات قبل الحفظ النهائي</p>
          
          <div className="summary">
            {formQuestions.map((question, index) => (
              <div key={index} className="summary-item">
                <strong>{question.question}:</strong>
                <span>
                  {Array.isArray(answers[index]) 
                    ? answers[index].join(', ') 
                    : answers[index] || 'غير محدد'
                  }
                </span>
              </div>
            ))}
          </div>

          <div className="completion-actions">
            <button className="edit-btn" onClick={() => {
              setIsCompleted(false)
              setCurrentQuestionIndex(formQuestions.length - 1)
            }}>
              تعديل
            </button>
            <button className="submit-btn" onClick={handleSubmit}>
              حفظ العقار
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="conversational-form">
      <div className="progress-bar">
        <div className="progress-fill" style={{ width: `${progress}%` }}></div>
        <span className="progress-text">{currentQuestionIndex + 1} من {formQuestions.length}</span>
      </div>

      <div className="question-container">
        <div className="question-bubble">
          <div className="question-icon">{currentQuestion.icon}</div>
          <h3 className="question-text">{currentQuestion.question}</h3>
          {currentQuestion.description && (
            <p className="question-description">{currentQuestion.description}</p>
          )}
        </div>

        {renderAnswerOptions()}

        {currentQuestionIndex > 0 && (
          <button className="previous-btn" onClick={handlePrevious}>
            ← السؤال السابق
          </button>
        )}
      </div>
    </div>
  )
}

export default ConversationalForm
