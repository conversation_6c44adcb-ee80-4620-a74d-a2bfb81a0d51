import { useState, useEffect } from 'react'
import './SavedProperties.css'

const SavedProperties = ({ onClose }) => {
  const [savedProperties, setSavedProperties] = useState([])
  const [searchTerm, setSearchTerm] = useState('')

  useEffect(() => {
    const properties = JSON.parse(localStorage.getItem('saved-properties') || '[]')
    setSavedProperties(properties)
  }, [])

  const filteredProperties = savedProperties.filter(property => 
    property.unitCode?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    property.unitType?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    property.region?.some(r => r.toLowerCase().includes(searchTerm.toLowerCase()))
  )

  const deleteProperty = (index) => {
    if (confirm('هل أنت متأكد من حذف هذا العقار؟')) {
      const updatedProperties = savedProperties.filter((_, i) => i !== index)
      setSavedProperties(updatedProperties)
      localStorage.setItem('saved-properties', JSON.stringify(updatedProperties))
    }
  }

  const exportToCSV = () => {
    if (savedProperties.length === 0) {
      alert('لا توجد عقارات محفوظة للتصدير')
      return
    }

    const headers = [
      'كود العقار',
      'نوع الوحدة', 
      'المنطقة',
      'المساحة',
      'السعر',
      'الموظف المسؤول',
      'تاريخ الإنشاء'
    ]

    const csvContent = [
      headers.join(','),
      ...savedProperties.map(property => [
        property.unitCode || '',
        property.unitType || '',
        Array.isArray(property.region) ? property.region.join('; ') : '',
        property.area || '',
        property.price || '',
        property.employeeName || '',
        property.createdAt ? new Date(property.createdAt).toLocaleDateString('ar-EG') : ''
      ].join(','))
    ].join('\n')

    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    link.href = URL.createObjectURL(blob)
    link.download = `عقارات_محفوظة_${new Date().toISOString().split('T')[0]}.csv`
    link.click()
  }

  return (
    <div className="saved-properties-overlay">
      <div className="saved-properties-modal">
        <div className="modal-header">
          <h2>العقارات المحفوظة ({savedProperties.length})</h2>
          <button className="close-btn" onClick={onClose}>×</button>
        </div>

        <div className="modal-controls">
          <input
            type="text"
            placeholder="البحث في العقارات..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="search-input"
          />
          <button className="export-btn" onClick={exportToCSV}>
            📊 تصدير CSV
          </button>
        </div>

        <div className="properties-list">
          {filteredProperties.length === 0 ? (
            <div className="empty-state">
              {searchTerm ? 'لا توجد نتائج للبحث' : 'لا توجد عقارات محفوظة'}
            </div>
          ) : (
            filteredProperties.map((property, index) => (
              <div key={index} className="property-card">
                <div className="property-header">
                  <h3>{property.unitCode}</h3>
                  <button 
                    className="delete-btn"
                    onClick={() => deleteProperty(index)}
                    title="حذف العقار"
                  >
                    🗑️
                  </button>
                </div>
                
                <div className="property-details">
                  <div className="detail-row">
                    <span className="label">النوع:</span>
                    <span className="value">{property.unitType || 'غير محدد'}</span>
                  </div>
                  
                  <div className="detail-row">
                    <span className="label">المنطقة:</span>
                    <span className="value">
                      {Array.isArray(property.region) 
                        ? property.region.join(', ') 
                        : property.region || 'غير محدد'
                      }
                    </span>
                  </div>
                  
                  <div className="detail-row">
                    <span className="label">المساحة:</span>
                    <span className="value">{property.area ? `${property.area} م²` : 'غير محدد'}</span>
                  </div>
                  
                  <div className="detail-row">
                    <span className="label">السعر:</span>
                    <span className="value">{property.price ? `${property.price} جنيه` : 'غير محدد'}</span>
                  </div>
                  
                  <div className="detail-row">
                    <span className="label">الموظف:</span>
                    <span className="value">{property.employeeName || 'غير محدد'}</span>
                  </div>
                  
                  <div className="detail-row">
                    <span className="label">تاريخ الإنشاء:</span>
                    <span className="value">
                      {property.createdAt 
                        ? new Date(property.createdAt).toLocaleDateString('ar-EG')
                        : 'غير محدد'
                      }
                    </span>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  )
}

export default SavedProperties
