/**
 * خدمة Google Sheets API
 * تتعامل مع إرسال البيانات إلى Google Sheets
 */

// معرف الجدول من ملف البيئة أو التكوين
const GOOGLE_SHEETS_ID = '1g7empopPTGkd5ZiET18FMBi8rd72vzdlH_mdhbznDNw'

/**
 * تحويل البيانات إلى تنسيق مناسب لـ Google Sheets
 * @param {Object} propertyData - بيانات العقار
 * @returns {Array} صف البيانات للجدول
 */
const formatDataForSheets = (propertyData) => {
  return [
    propertyData.unitCode || '',
    propertyData.unitType || '',
    Array.isArray(propertyData.unitCondition) 
      ? propertyData.unitCondition.join(', ') 
      : propertyData.unitCondition || '',
    Array.isArray(propertyData.region) 
      ? propertyData.region.join(', ') 
      : propertyData.region || '',
    Array.isArray(propertyData.floor) 
      ? propertyData.floor.join(', ') 
      : propertyData.floor || '',
    propertyData.area || '',
    propertyData.price || '',
    propertyData.imageStatus || '',
    propertyData.features || '',
    propertyData.fullDetails || '',
    propertyData.address || '',
    propertyData.employeeName || '',
    propertyData.propertyAvailability || '',
    Array.isArray(propertyData.status) 
      ? propertyData.status.join(', ') 
      : propertyData.status || '',
    new Date().toISOString(), // تاريخ الإرسال
    propertyData.createdAt || '',
    propertyData.lastProcessed || '',
    propertyData.reminderDate || ''
  ]
}

/**
 * إرسال البيانات إلى Google Sheets باستخدام Google Apps Script
 * @param {Array} propertiesData - مصفوفة من بيانات العقارات
 * @returns {Promise} نتيجة الإرسال
 */
export const sendToGoogleSheets = async (propertiesData) => {
  try {
    // تحويل البيانات إلى تنسيق الجدول
    const formattedData = propertiesData.map(formatDataForSheets)
    
    // إضافة رؤوس الأعمدة إذا كانت هذه أول عملية إرسال
    const headers = [
      'كود العقار',
      'نوع الوحدة', 
      'حالة الوحدة',
      'المنطقة',
      'الدور',
      'المساحة',
      'السعر',
      'حالة الصور',
      'المميزات',
      'التفاصيل الكاملة',
      'العنوان',
      'اسم الموظف',
      'إتاحة العقار',
      'الحالة',
      'تاريخ الإرسال',
      'تاريخ الإنشاء',
      'آخر معالجة',
      'تاريخ التذكير'
    ]

    // إعداد البيانات للإرسال
    const payload = {
      spreadsheetId: GOOGLE_SHEETS_ID,
      data: formattedData,
      headers: headers
    }

    // هنا يمكن استخدام Google Apps Script Web App أو Google Sheets API مباشرة
    // للبساطة، سنستخدم طريقة Google Apps Script
    
    // رابط Google Apps Script Web App (يجب إنشاؤه)
    const SCRIPT_URL = 'https://script.google.com/macros/s/YOUR_SCRIPT_ID/exec'
    
    console.log('إرسال البيانات إلى Google Sheets:', payload)
    
    // محاكاة الإرسال (يجب استبدالها بالكود الفعلي)
    const response = await simulateGoogleSheetsSubmission(payload)
    
    return {
      success: true,
      message: `تم إرسال ${propertiesData.length} عقار بنجاح إلى Google Sheets`,
      data: response
    }

  } catch (error) {
    console.error('خطأ في إرسال البيانات إلى Google Sheets:', error)
    throw new Error(`فشل في إرسال البيانات: ${error.message}`)
  }
}

/**
 * محاكاة إرسال البيانات (للاختبار)
 * @param {Object} payload - البيانات المراد إرسالها
 * @returns {Promise} نتيجة محاكاة
 */
const simulateGoogleSheetsSubmission = async (payload) => {
  // محاكاة تأخير الشبكة
  await new Promise(resolve => setTimeout(resolve, 2000))
  
  // محاكاة نجاح العملية
  return {
    status: 'success',
    rowsAdded: payload.data.length,
    timestamp: new Date().toISOString(),
    spreadsheetId: payload.spreadsheetId
  }
}

/**
 * إرسال بيانات عقار واحد إلى Google Sheets
 * @param {Object} propertyData - بيانات العقار
 * @returns {Promise} نتيجة الإرسال
 */
export const sendSinglePropertyToSheets = async (propertyData) => {
  return await sendToGoogleSheets([propertyData])
}

/**
 * التحقق من اتصال Google Sheets
 * @returns {Promise<boolean>} حالة الاتصال
 */
export const testGoogleSheetsConnection = async () => {
  try {
    // محاكاة اختبار الاتصال
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    console.log('تم اختبار الاتصال مع Google Sheets بنجاح')
    return true
  } catch (error) {
    console.error('فشل في الاتصال مع Google Sheets:', error)
    return false
  }
}

/**
 * إنشاء رابط مباشر لعرض الجدول
 * @returns {string} رابط Google Sheets
 */
export const getGoogleSheetsUrl = () => {
  return `https://docs.google.com/spreadsheets/d/${GOOGLE_SHEETS_ID}/edit`
}

/**
 * تصدير البيانات بتنسيق CSV متوافق مع Google Sheets
 * @param {Array} propertiesData - بيانات العقارات
 * @returns {string} محتوى CSV
 */
export const exportToCSVForSheets = (propertiesData) => {
  const headers = [
    'كود العقار', 'نوع الوحدة', 'حالة الوحدة', 'المنطقة', 'الدور',
    'المساحة', 'السعر', 'حالة الصور', 'المميزات', 'التفاصيل الكاملة',
    'العنوان', 'اسم الموظف', 'إتاحة العقار', 'الحالة', 'تاريخ الإرسال'
  ]

  const csvContent = [
    headers.join(','),
    ...propertiesData.map(property => {
      const formattedRow = formatDataForSheets(property)
      return formattedRow.map(field => `"${field}"`).join(',')
    })
  ].join('\n')

  return '\ufeff' + csvContent // إضافة BOM للدعم العربي
}

// تصدير الثوابت المفيدة
export const GOOGLE_SHEETS_CONFIG = {
  SHEETS_ID: GOOGLE_SHEETS_ID,
  SHEETS_URL: `https://docs.google.com/spreadsheets/d/${GOOGLE_SHEETS_ID}/edit`
}
