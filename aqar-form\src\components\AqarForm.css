.aqar-form-container {
  width: 100%;
  max-width: 800px;
  background: white;
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  position: relative;
}

.form-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 30px;
  text-align: center;
  position: relative;
}

.form-title {
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 15px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-controls {
  position: absolute;
  top: 20px;
  left: 20px;
  display: flex;
  gap: 10px;
}

.mode-toggle-btn,
.saved-properties-btn,
.data-submission-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50px;
  padding: 10px 20px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  white-space: nowrap;
}

.mode-toggle-btn:hover,
.saved-properties-btn:hover,
.data-submission-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
}

/* تأثير خاص لزر الإرسال */
.data-submission-btn {
  background: rgba(40, 167, 69, 0.3);
  border-color: rgba(40, 167, 69, 0.5);
}

.data-submission-btn:hover {
  background: rgba(40, 167, 69, 0.5);
  border-color: rgba(40, 167, 69, 0.7);
}

.mode-toggle-btn span {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* تحسينات للهواتف */
@media (max-width: 768px) {
  .aqar-form-container {
    margin: 0;
    border-radius: 0;
    min-height: 100vh;
  }
  
  .form-header {
    padding: 20px;
    border-radius: 0;
  }
  
  .form-title {
    font-size: 24px;
    margin-bottom: 10px;
  }
  
  .header-controls {
    position: static;
    margin-top: 15px;
    width: 100%;
    justify-content: center;
    flex-direction: column;
    gap: 8px;
  }

  .mode-toggle-btn,
  .saved-properties-btn,
  .data-submission-btn {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .form-header {
    padding: 15px;
  }
  
  .form-title {
    font-size: 20px;
  }
  
  .mode-toggle-btn,
  .saved-properties-btn,
  .data-submission-btn {
    padding: 8px 16px;
    font-size: 12px;
  }
}

/* تأثيرات انتقالية */
.form-content {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* تأثير التموج للأزرار */
.mode-toggle-btn {
  position: relative;
  overflow: hidden;
}

.mode-toggle-btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transition: width 0.6s, height 0.6s;
  transform: translate(-50%, -50%);
  z-index: 0;
}

.mode-toggle-btn:active::before {
  width: 300px;
  height: 300px;
}

.mode-toggle-btn span {
  position: relative;
  z-index: 1;
}
