.traditional-form {
  padding: 0;
  min-height: 600px;
}

.form-progress {
  background: #f8f9fa;
  padding: 25px 30px;
  border-bottom: 1px solid #e9ecef;
}

.progress-bar {
  background: #e9ecef;
  height: 6px;
  border-radius: 3px;
  margin-bottom: 20px;
  overflow: hidden;
}

.progress-fill {
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  height: 100%;
  border-radius: 3px;
  transition: width 0.5s ease;
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.step-indicators {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.step-indicator {
  display: flex;
  align-items: center;
  gap: 10px;
  flex: 1;
  position: relative;
}

.step-indicator:not(:last-child)::after {
  content: '';
  position: absolute;
  top: 15px;
  left: calc(100% - 20px);
  width: 40px;
  height: 2px;
  background: #dee2e6;
  z-index: 1;
}

.step-indicator.active:not(:last-child)::after {
  background: #667eea;
}

.step-number {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: #dee2e6;
  color: #6c757d;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
  transition: all 0.3s ease;
  z-index: 2;
  position: relative;
}

.step-indicator.active .step-number {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  transform: scale(1.1);
}

.step-title {
  font-size: 14px;
  font-weight: 600;
  color: #6c757d;
  transition: color 0.3s ease;
}

.step-indicator.active .step-title {
  color: #667eea;
}

.form-content {
  padding: 40px;
}

.step-header {
  text-align: center;
  margin-bottom: 40px;
}

.step-header .step-title {
  font-size: 28px;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 10px;
}

.step-description {
  font-size: 16px;
  color: #6c757d;
  margin: 0;
}

.form-fields {
  display: grid;
  gap: 25px;
  max-width: 600px;
  margin: 0 auto;
}

.form-field {
  position: relative;
}

.field-label {
  display: block;
  font-size: 16px;
  font-weight: 600;
  color: #495057;
  margin-bottom: 8px;
  position: relative;
}

.required {
  color: #dc3545;
  margin-right: 4px;
}

.input-container {
  position: relative;
}

.field-input {
  width: 100%;
  padding: 15px 20px;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  font-size: 16px;
  transition: all 0.3s ease;
  background: white;
  font-family: inherit;
}

.field-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  transform: translateY(-1px);
}

.field-input.error {
  border-color: #dc3545;
  box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
}

.input-icon {
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 18px;
  color: #6c757d;
  pointer-events: none;
}

.field-input:focus + .input-icon {
  color: #667eea;
}

.error-message {
  color: #dc3545;
  font-size: 14px;
  margin-top: 5px;
  display: block;
  animation: slideInDown 0.3s ease;
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.multi-select-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 10px;
  margin-top: 10px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px 15px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
}

.checkbox-label:hover {
  border-color: #667eea;
  background: #f8f9ff;
}

.checkbox-label input[type="checkbox"] {
  width: 18px;
  height: 18px;
  accent-color: #667eea;
  cursor: pointer;
}

.checkbox-label input[type="checkbox"]:checked + .checkbox-text {
  color: #667eea;
  font-weight: 600;
}

.checkbox-text {
  font-size: 14px;
  color: #495057;
  transition: all 0.3s ease;
}

.form-actions {
  display: grid;
  grid-template-columns: 1fr auto 1fr;
  align-items: center;
  gap: 15px;
  margin-top: 40px;
  padding-top: 30px;
  border-top: 1px solid #e9ecef;
}

.action-left {
  justify-self: start;
}

.action-center {
  justify-self: center;
}

.action-right {
  justify-self: end;
}

.form-actions .btn {
  min-width: 120px;
  padding: 15px 30px;
  font-size: 16px;
  border-radius: 50px;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.btn-secondary {
  background: white;
  color: #6c757d;
  border: 2px solid #e9ecef;
}

.btn-outline {
  background: transparent;
  color: #667eea;
  border: 2px solid #667eea;
  font-size: 14px;
  padding: 12px 20px;
}

.btn-outline:hover {
  background: #667eea;
  color: white;
  transform: translateY(-1px);
}

.btn-primary:hover {
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.btn-secondary:hover {
  border-color: #6c757d;
  background: #f8f9fa;
}

/* تحسينات للهواتف */
@media (max-width: 768px) {
  .traditional-form {
    min-height: auto;
  }
  
  .form-progress {
    padding: 20px;
  }
  
  .step-indicators {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
  }
  
  .step-indicator {
    flex-direction: row;
    width: 100%;
  }
  
  .step-indicator:not(:last-child)::after {
    display: none;
  }
  
  .form-content {
    padding: 25px 20px;
  }
  
  .step-header .step-title {
    font-size: 24px;
  }
  
  .multi-select-options {
    grid-template-columns: 1fr;
  }
  
  .form-actions {
    grid-template-columns: 1fr;
    gap: 12px;
    text-align: center;
  }

  .action-left,
  .action-center,
  .action-right {
    justify-self: center;
  }

  .form-actions .btn {
    width: 100%;
    max-width: 300px;
  }
}

@media (max-width: 480px) {
  .form-progress {
    padding: 15px;
  }
  
  .form-content {
    padding: 20px 15px;
  }
  
  .step-header .step-title {
    font-size: 20px;
  }
  
  .field-input {
    padding: 12px 15px;
    font-size: 14px;
  }
  
  .checkbox-label {
    padding: 10px 12px;
  }
  
  .checkbox-text {
    font-size: 13px;
  }
}

/* تأثيرات إضافية */
.form-field {
  animation: fadeInUp 0.5s ease;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* تأثير التركيز المحسن */
.field-input:focus {
  position: relative;
}

.field-input:focus::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #667eea, #764ba2);
  border-radius: 14px;
  z-index: -1;
  opacity: 0.1;
}
