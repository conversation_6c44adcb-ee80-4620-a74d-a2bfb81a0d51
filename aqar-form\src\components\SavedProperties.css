.saved-properties-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
  backdrop-filter: blur(5px);
}

.saved-properties-modal {
  background: white;
  border-radius: 20px;
  width: 100%;
  max-width: 900px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
  animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 25px 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h2 {
  margin: 0;
  font-size: 24px;
  font-weight: 700;
}

.close-btn {
  background: none;
  border: none;
  color: white;
  font-size: 30px;
  cursor: pointer;
  padding: 0;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: rotate(90deg);
}

.modal-controls {
  padding: 20px 30px;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  gap: 15px;
  align-items: center;
}

.search-input {
  flex: 1;
  padding: 12px 20px;
  border: 2px solid #e9ecef;
  border-radius: 25px;
  font-size: 16px;
  transition: all 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.export-btn {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 25px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.export-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.properties-list {
  max-height: 60vh;
  overflow-y: auto;
  padding: 20px 30px;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #6c757d;
  font-size: 18px;
}

.property-card {
  background: #f8f9fa;
  border-radius: 15px;
  padding: 20px;
  margin-bottom: 15px;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
  animation: fadeInUp 0.5s ease;
}

.property-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: #667eea;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.property-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #dee2e6;
}

.property-header h3 {
  margin: 0;
  color: #667eea;
  font-size: 18px;
  font-weight: 700;
}

.delete-btn {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.delete-btn:hover {
  background: #dc3545;
  transform: scale(1.1);
}

.property-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 12px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.label {
  font-weight: 600;
  color: #495057;
  min-width: 80px;
}

.value {
  color: #6c757d;
  text-align: left;
  flex: 1;
  margin-right: 10px;
}

/* تحسينات للهواتف */
@media (max-width: 768px) {
  .saved-properties-overlay {
    padding: 10px;
  }
  
  .saved-properties-modal {
    max-height: 95vh;
    border-radius: 15px;
  }
  
  .modal-header {
    padding: 20px;
  }
  
  .modal-header h2 {
    font-size: 20px;
  }
  
  .modal-controls {
    padding: 15px 20px;
    flex-direction: column;
    gap: 10px;
  }
  
  .search-input {
    width: 100%;
  }
  
  .export-btn {
    width: 100%;
    padding: 15px;
  }
  
  .properties-list {
    padding: 15px 20px;
    max-height: 65vh;
  }
  
  .property-card {
    padding: 15px;
    border-radius: 12px;
  }
  
  .property-header h3 {
    font-size: 16px;
  }
  
  .property-details {
    grid-template-columns: 1fr;
    gap: 8px;
  }
  
  .detail-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
    padding: 6px 0;
  }
  
  .label {
    min-width: auto;
    font-size: 14px;
  }
  
  .value {
    text-align: right;
    margin-right: 0;
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .modal-header {
    padding: 15px;
  }
  
  .modal-header h2 {
    font-size: 18px;
  }
  
  .close-btn {
    width: 35px;
    height: 35px;
    font-size: 25px;
  }
  
  .modal-controls {
    padding: 12px 15px;
  }
  
  .properties-list {
    padding: 12px 15px;
  }
  
  .property-card {
    padding: 12px;
  }
  
  .empty-state {
    padding: 40px 15px;
    font-size: 16px;
  }
}

/* تأثيرات إضافية */
.property-card:last-child {
  margin-bottom: 0;
}

.properties-list::-webkit-scrollbar {
  width: 8px;
}

.properties-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.properties-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.properties-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
