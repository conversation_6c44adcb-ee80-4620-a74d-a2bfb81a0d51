.google-form-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
  backdrop-filter: blur(8px);
}

.google-form-modal {
  background: white;
  border-radius: 20px;
  width: 100%;
  max-width: 1000px;
  max-height: 95vh;
  overflow: hidden;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
  animation: modalSlideIn 0.4s ease;
  display: flex;
  flex-direction: column;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 25px 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}

.modal-header h2 {
  margin: 0;
  font-size: 24px;
  font-weight: 700;
}

.close-btn {
  background: none;
  border: none;
  color: white;
  font-size: 30px;
  cursor: pointer;
  padding: 0;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: rotate(90deg);
}

/* إعداد النموذج */
.form-setup {
  padding: 40px;
  text-align: center;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.setup-content {
  max-width: 600px;
  width: 100%;
}

.setup-content h3 {
  color: #333;
  font-size: 28px;
  margin-bottom: 15px;
  font-weight: 700;
}

.setup-content p {
  color: #666;
  font-size: 16px;
  margin-bottom: 30px;
  line-height: 1.6;
}

.form-id-form {
  background: #f8f9fa;
  padding: 30px;
  border-radius: 15px;
  margin-bottom: 30px;
}

.input-group {
  text-align: right;
  margin-bottom: 20px;
}

.input-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #333;
}

.form-id-input {
  width: 100%;
  padding: 15px;
  border: 2px solid #e9ecef;
  border-radius: 10px;
  font-size: 16px;
  transition: all 0.3s ease;
  direction: ltr;
  text-align: left;
}

.form-id-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.help-text {
  display: block;
  margin-top: 8px;
  color: #6c757d;
  font-size: 14px;
  line-height: 1.4;
}

.form-actions {
  text-align: center;
}

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #5a6268;
  transform: translateY(-1px);
}

.btn-outline {
  background: transparent;
  color: #667eea;
  border: 2px solid #667eea;
}

.btn-outline:hover {
  background: #667eea;
  color: white;
}

/* دليل الإعداد */
.setup-guide {
  background: #e8f4fd;
  border: 1px solid #b8daff;
  border-radius: 10px;
  padding: 20px;
  text-align: right;
}

.setup-guide h4 {
  color: #004085;
  margin-bottom: 15px;
  font-size: 18px;
}

.setup-guide ol {
  color: #004085;
  line-height: 1.8;
  padding-right: 20px;
}

.setup-guide code {
  background: #f8f9fa;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  color: #e83e8c;
}

.setup-guide strong {
  color: #dc3545;
}

/* حاوية النموذج */
.form-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.form-controls {
  background: #f8f9fa;
  padding: 20px 30px;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}

.form-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.form-status {
  color: #28a745;
  font-weight: 600;
  font-size: 14px;
}

.form-id-display {
  color: #6c757d;
  font-size: 14px;
  font-family: 'Courier New', monospace;
}

.form-actions {
  display: flex;
  gap: 10px;
}

/* إطار النموذج */
.iframe-container {
  flex: 1;
  position: relative;
  overflow: hidden;
}

.google-form-iframe {
  width: 100%;
  height: 100%;
  border: none;
  background: white;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-overlay p {
  color: #666;
  font-size: 16px;
  margin: 0;
}

/* تذييل النموذج */
.form-footer {
  background: #f8f9fa;
  padding: 20px 30px;
  border-top: 1px solid #e9ecef;
  flex-shrink: 0;
}

.footer-info p {
  margin: 0 0 8px 0;
  color: #6c757d;
  font-size: 14px;
  line-height: 1.5;
}

.footer-info p:last-child {
  margin-bottom: 0;
}

/* الاستجابة للهواتف المحمولة */
@media (max-width: 768px) {
  .google-form-overlay {
    padding: 10px;
  }
  
  .google-form-modal {
    max-height: 98vh;
    border-radius: 15px;
  }
  
  .modal-header {
    padding: 20px;
  }
  
  .modal-header h2 {
    font-size: 20px;
  }
  
  .form-setup {
    padding: 30px 20px;
  }
  
  .setup-content h3 {
    font-size: 24px;
  }
  
  .form-id-form {
    padding: 20px;
  }
  
  .form-controls {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
    padding: 15px 20px;
  }
  
  .form-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .form-actions {
    justify-content: center;
  }
  
  .form-footer {
    padding: 15px 20px;
  }
}
