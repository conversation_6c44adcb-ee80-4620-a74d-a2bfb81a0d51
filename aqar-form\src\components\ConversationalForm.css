.conversational-form {
  padding: 30px;
  min-height: 500px;
  display: flex;
  flex-direction: column;
}

.progress-bar {
  background: #f0f0f0;
  height: 8px;
  border-radius: 4px;
  margin-bottom: 30px;
  position: relative;
  overflow: hidden;
}

.progress-fill {
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  height: 100%;
  border-radius: 4px;
  transition: width 0.5s ease;
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.progress-text {
  position: absolute;
  top: -25px;
  right: 0;
  font-size: 12px;
  color: #666;
  font-weight: 600;
}

.question-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
}

.question-bubble {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 30px;
  border-radius: 20px;
  margin-bottom: 30px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  max-width: 600px;
  position: relative;
  animation: slideInDown 0.6s ease;
}

.question-bubble::before {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 15px solid transparent;
  border-right: 15px solid transparent;
  border-top: 15px solid #e9ecef;
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.question-icon {
  font-size: 40px;
  margin-bottom: 15px;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-10px); }
  60% { transform: translateY(-5px); }
}

.question-text {
  font-size: 24px;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 10px;
  line-height: 1.4;
}

.question-description {
  font-size: 16px;
  color: #6c757d;
  margin: 0;
  line-height: 1.5;
}

.answer-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  max-width: 600px;
  width: 100%;
}

.answer-btn {
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  padding: 20px;
  font-size: 16px;
  font-weight: 600;
  color: #495057;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  animation: fadeInUp 0.5s ease;
}

.answer-btn:hover {
  border-color: #667eea;
  color: #667eea;
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
}

.answer-btn.selected {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: #667eea;
  color: white;
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.multi-select-container {
  width: 100%;
  max-width: 600px;
}

.continue-btn {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  border: none;
  border-radius: 50px;
  padding: 15px 40px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  margin-top: 20px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.continue-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
}

.continue-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.text-input-container {
  width: 100%;
  max-width: 400px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.text-input {
  padding: 20px;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  font-size: 18px;
  text-align: center;
  transition: all 0.3s ease;
  background: white;
}

.text-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.previous-btn {
  position: absolute;
  bottom: 30px;
  right: 30px;
  background: transparent;
  border: 2px solid #6c757d;
  color: #6c757d;
  border-radius: 50px;
  padding: 10px 20px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.previous-btn:hover {
  background: #6c757d;
  color: white;
}

/* شاشة الإكمال */
.completion-screen {
  padding: 40px;
  text-align: center;
  min-height: 500px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.completion-content {
  max-width: 600px;
  width: 100%;
}

.success-icon {
  font-size: 80px;
  margin-bottom: 20px;
  animation: bounce 1s ease;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-20px); }
  60% { transform: translateY(-10px); }
}

.completion-content h2 {
  font-size: 28px;
  color: #28a745;
  margin-bottom: 15px;
  font-weight: 700;
}

.completion-content p {
  font-size: 16px;
  color: #6c757d;
  margin-bottom: 30px;
}

.summary {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 25px;
  margin-bottom: 30px;
  text-align: right;
  max-height: 300px;
  overflow-y: auto;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 10px 0;
  border-bottom: 1px solid #e9ecef;
  gap: 15px;
}

.summary-item:last-child {
  border-bottom: none;
}

.summary-item strong {
  color: #495057;
  font-weight: 600;
  min-width: 150px;
  text-align: right;
}

.summary-item span {
  color: #6c757d;
  text-align: left;
  flex: 1;
}

.completion-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
}

.edit-btn {
  background: #6c757d;
  color: white;
  border: none;
  border-radius: 50px;
  padding: 15px 30px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.submit-btn {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  border: none;
  border-radius: 50px;
  padding: 15px 40px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.edit-btn:hover,
.submit-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

/* تحسينات للهواتف */
@media (max-width: 768px) {
  .conversational-form {
    padding: 15px;
    min-height: calc(100vh - 120px);
  }

  .progress-bar {
    margin-bottom: 20px;
  }

  .question-bubble {
    padding: 20px;
    margin-bottom: 20px;
    border-radius: 15px;
  }

  .question-text {
    font-size: 18px;
    line-height: 1.3;
  }

  .question-description {
    font-size: 14px;
  }

  .answer-options {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .answer-btn {
    padding: 18px 15px;
    font-size: 15px;
    border-radius: 10px;
    text-align: center;
  }

  .text-input {
    padding: 18px;
    font-size: 16px;
    border-radius: 10px;
  }

  .continue-btn {
    padding: 18px 35px;
    font-size: 15px;
    border-radius: 25px;
  }

  .previous-btn {
    position: static;
    margin-top: 20px;
    width: 100%;
    padding: 12px;
    border-radius: 25px;
  }

  .completion-screen {
    padding: 20px;
    min-height: calc(100vh - 120px);
  }

  .success-icon {
    font-size: 60px;
  }

  .completion-content h2 {
    font-size: 24px;
  }

  .completion-actions {
    flex-direction: column;
    gap: 12px;
  }

  .edit-btn,
  .submit-btn {
    width: 100%;
    padding: 18px;
    font-size: 16px;
    border-radius: 25px;
  }

  .summary {
    padding: 20px;
    border-radius: 10px;
    max-height: 250px;
  }

  .summary-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
    padding: 12px 0;
  }

  .summary-item strong {
    min-width: auto;
    font-size: 14px;
  }

  .summary-item span {
    font-size: 14px;
    text-align: right;
  }
}

@media (max-width: 480px) {
  .conversational-form {
    padding: 10px;
  }

  .question-bubble {
    padding: 15px;
    margin-bottom: 15px;
  }

  .question-text {
    font-size: 16px;
  }

  .answer-btn {
    padding: 15px 12px;
    font-size: 14px;
  }

  .text-input {
    padding: 15px;
    font-size: 15px;
  }

  .continue-btn {
    padding: 15px 30px;
    font-size: 14px;
  }

  .progress-text {
    font-size: 11px;
  }
}
