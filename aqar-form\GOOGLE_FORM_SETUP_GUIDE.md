# دليل إنشاء Google Form للعقارات

## خطوات إنشاء النموذج

### 1. إنشاء نموذج جديد
1. اذهب إلى [Google Forms](https://forms.google.com)
2. انق<PERSON> على "إنشاء نموذج جديد" أو "+"
3. اكتب عنوان النموذج: "نموذج بيانات العقارات"
4. اكتب وصف: "نموذج لإدخال وإرسال بيانات العقارات إلى قاعدة البيانات"

### 2. إعداد الحقول (بالترتيب)

#### الحقل 1: نوع الوحدة (مطلوب)
- **نوع السؤال**: اختيار من متعدد
- **السؤال**: ما نوع الوحدة؟
- **الخيارات**:
  - مكتب
  - مخزن
  - عيادة
  - عمارة
  - غرفة
  - أرض
  - تاون هاوس
  - بيزمنت
  - روف
  - غير محدد
  - شقة
  - بنتاهاوس
  - فيلا
- **مطلوب**: نعم

#### الحقل 2: حالة الوحدة (مطلوب)
- **نوع السؤال**: مربعات اختيار (اختيار متعدد)
- **السؤال**: ما حالة الوحدة؟ (يمكنك اختيار أكثر من خيار)
- **الخيارات**:
  - مفروش
  - فاضي
  - تمليك
  - نصف تشطيب
  - إداري
  - طبي
- **مطلوب**: نعم

#### الحقل 3: المنطقة (مطلوب)
- **نوع السؤال**: مربعات اختيار (اختيار متعدد)
- **السؤال**: في أي منطقة يقع العقار؟
- **الخيارات**:
  - غير محدد
  - إسكان شباب
  - سكن شباب
  - قطامية
  - نزهة ثالث
  - مستقبل
  - هناجر
  - سكن معارض
  - سكن أندلس
  - حي أندلس
  - دار أندلس
  - جنة
  - جاردينيا هايتس
  - أحياء تجمع
  - مستثمرين
  - شويفات
  - نرجس عمارات
  - نرجس فيلات
  - لوتس
  - زبرينا
  - بيت وطن
  - تجمع خامس
  - دار قرنفل
  - بنفسج عمارات
  - بنفسج فيلات
  - ياسمين
  - شروق
  - كمباوندات
- **مطلوب**: نعم

#### الحقل 4: الدور
- **نوع السؤال**: مربعات اختيار (اختيار متعدد)
- **السؤال**: في أي دور يقع العقار؟ (يمكنك اختيار أكثر من دور)
- **الخيارات**:
  - غير محدد
  - بيزمنت
  - أرضي
  - أرضي مرتفع
  - دور أول
  - دور ثاني
  - دور ثالث
  - دور رابع
  - دور خامس
  - دور سادس
  - دور سابع
  - دور ثامن
  - متكرر
- **مطلوب**: لا

#### الحقل 5: المساحة (مطلوب)
- **نوع السؤال**: إجابة قصيرة
- **السؤال**: كم مساحة العقار؟
- **نص المساعدة**: أدخل المساحة بالمتر المربع
- **التحقق**: رقم، أكبر من 0
- **مطلوب**: نعم

#### الحقل 6: السعر (مطلوب)
- **نوع السؤال**: إجابة قصيرة
- **السؤال**: كم سعر العقار؟
- **نص المساعدة**: أدخل السعر بالجنيه المصري
- **التحقق**: رقم، أكبر من 0
- **مطلوب**: نعم

#### الحقل 7: حالة الصور
- **نوع السؤال**: اختيار من متعدد
- **السؤال**: ما حالة الصور؟
- **الخيارات**:
  - غير محدد
  - بصور
  - بدون صور
- **مطلوب**: لا

#### الحقل 8: المميزات
- **نوع السؤال**: فقرة
- **السؤال**: ما هي مميزات العقار؟
- **نص المساعدة**: اذكر المميزات الخاصة بالعقار
- **مطلوب**: لا

#### الحقل 9: التفاصيل الكاملة
- **نوع السؤال**: فقرة
- **السؤال**: أضف التفاصيل الكاملة للعقار
- **نص المساعدة**: اكتب وصف مفصل للعقار
- **مطلوب**: لا

#### الحقل 10: العنوان
- **نوع السؤال**: إجابة قصيرة
- **السؤال**: ما عنوان العقار؟
- **نص المساعدة**: أدخل العنوان التفصيلي
- **مطلوب**: لا

#### الحقل 11: الموظف المسؤول (مطلوب)
- **نوع السؤال**: اختيار من متعدد
- **السؤال**: من الموظف المسؤول؟
- **الخيارات**:
  - غير محدد
  - إسلام
  - تاحة
  - بلبل
  - أيمن
  - علياء
  - محمود سامي
  - يوسف عماد
  - يوسف الجوهري
  - محمد نبيل
- **مطلوب**: نعم

#### الحقل 12: إتاحة العقار (مطلوب)
- **نوع السؤال**: اختيار من متعدد
- **السؤال**: ما إتاحة العقار؟
- **الخيارات**:
  - غير متاح
  - غير محدد
  - مؤجر
  - متاح
  - اتباعت
  - هتفضي في تاريخ
  - معندوش نية حالياً
  - اتأجرت عن طريق المكتب
  - المالك لا يرد
  - الرقم مغلق
  - الرقم مشغول
- **مطلوب**: نعم

#### الحقل 13: حالة العقار
- **نوع السؤال**: مربعات اختيار (اختيار متعدد)
- **السؤال**: ما حالة العقار؟ (يمكنك اختيار أكثر من حالة)
- **الخيارات**:
  - عقار مكتب
  - عقار تالف
  - عقار متعدد
  - عقار مكرر
  - عقار ناجح
  - عقار فاشل
- **مطلوب**: لا

### 3. ربط النموذج بـ Google Sheets

1. في النموذج، انقر على علامة التبويب "الردود"
2. انقر على أيقونة Google Sheets (الجدول الأخضر)
3. اختر "إنشاء جدول بيانات جديد"
4. اكتب اسم الجدول: "بيانات العقارات"
5. انقر على "إنشاء"

### 4. إعدادات إضافية

#### إعدادات النموذج:
- **جمع عناوين البريد الإلكتروني**: لا (اختياري)
- **تقييد إلى نطاق واحد**: لا
- **تحديد الردود**: لا
- **تحرير بعد الإرسال**: نعم
- **عرض ملخص الردود**: لا

#### رسالة التأكيد:
"تم إرسال بيانات العقار بنجاح! شكراً لك."

### 5. الحصول على معرف النموذج

1. انسخ رابط النموذج
2. المعرف هو الجزء بين `/forms/d/` و `/edit`
3. مثال: `https://forms.google.com/forms/d/ABC123DEF456/edit`
4. المعرف هو: `ABC123DEF456`

### 6. الحصول على معرف الجدول

1. افتح Google Sheets المرتبط
2. انسخ الرابط
3. المعرف هو الجزء بين `/spreadsheets/d/` و `/edit`

## ملاحظات مهمة

- تأكد من أن جميع الحقول المطلوبة محددة كـ "مطلوب"
- استخدم نفس أسماء الحقول الموجودة في التطبيق
- تأكد من ترتيب الحقول كما هو موضح
- احفظ معرفات النموذج والجدول لاستخدامها في التطبيق
