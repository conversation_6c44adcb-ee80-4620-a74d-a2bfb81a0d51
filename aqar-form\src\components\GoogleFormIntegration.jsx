import { useState, useEffect } from 'react'
import './GoogleFormIntegration.css'

const GoogleFormIntegration = ({ onClose }) => {
  const [formUrl, setFormUrl] = useState('')
  const [isLoading, setIsLoading] = useState(true)
  const [formId, setFormId] = useState('')

  useEffect(() => {
    // تحميل معرف النموذج من التكوين
    const savedFormId = localStorage.getItem('google-form-id') || ''
    setFormId(savedFormId)
    
    if (savedFormId) {
      generateFormUrl(savedFormId)
    } else {
      setIsLoading(false)
    }
  }, [])

  const generateFormUrl = (id) => {
    if (id) {
      // إنشاء رابط النموذج المدمج
      const embedUrl = `https://docs.google.com/forms/d/${id}/viewform?embedded=true`
      setFormUrl(embedUrl)
      setIsLoading(false)
    }
  }

  const handleFormIdSubmit = (e) => {
    e.preventDefault()
    if (formId.trim()) {
      localStorage.setItem('google-form-id', formId.trim())
      generateFormUrl(formId.trim())
    }
  }

  const openFormInNewTab = () => {
    if (formId) {
      const fullUrl = `https://docs.google.com/forms/d/${formId}/viewform`
      window.open(fullUrl, '_blank')
    }
  }

  const resetForm = () => {
    setFormId('')
    setFormUrl('')
    localStorage.removeItem('google-form-id')
    setIsLoading(false)
  }

  return (
    <div className="google-form-overlay">
      <div className="google-form-modal">
        <div className="modal-header">
          <h2>نموذج Google Form للعقارات</h2>
          <button className="close-btn" onClick={onClose}>×</button>
        </div>

        {!formUrl ? (
          <div className="form-setup">
            <div className="setup-content">
              <h3>إعداد Google Form</h3>
              <p>لاستخدام Google Form، يرجى إدخال معرف النموذج:</p>
              
              <form onSubmit={handleFormIdSubmit} className="form-id-form">
                <div className="input-group">
                  <label htmlFor="formId">معرف Google Form:</label>
                  <input
                    type="text"
                    id="formId"
                    value={formId}
                    onChange={(e) => setFormId(e.target.value)}
                    placeholder="مثال: 1FAIpQLSe..."
                    className="form-id-input"
                  />
                  <small className="help-text">
                    يمكنك العثور على المعرف في رابط النموذج بين /forms/d/ و /edit
                  </small>
                </div>
                
                <div className="form-actions">
                  <button type="submit" className="btn btn-primary">
                    تحميل النموذج
                  </button>
                </div>
              </form>

              <div className="setup-guide">
                <h4>كيفية الحصول على معرف النموذج:</h4>
                <ol>
                  <li>افتح Google Form الخاص بك</li>
                  <li>انسخ الرابط من شريط العناوين</li>
                  <li>المعرف هو الجزء بين <code>/forms/d/</code> و <code>/edit</code></li>
                  <li>مثال: <code>https://forms.google.com/forms/d/<strong>ABC123</strong>/edit</code></li>
                </ol>
              </div>
            </div>
          </div>
        ) : (
          <div className="form-container">
            <div className="form-controls">
              <div className="form-info">
                <span className="form-status">✅ النموذج محمل بنجاح</span>
                <span className="form-id-display">المعرف: {formId.substring(0, 20)}...</span>
              </div>
              
              <div className="form-actions">
                <button 
                  className="btn btn-secondary"
                  onClick={openFormInNewTab}
                  title="فتح في نافذة جديدة"
                >
                  🔗 فتح في نافذة جديدة
                </button>
                
                <button 
                  className="btn btn-outline"
                  onClick={resetForm}
                  title="تغيير النموذج"
                >
                  ⚙️ تغيير النموذج
                </button>
              </div>
            </div>

            <div className="iframe-container">
              {isLoading && (
                <div className="loading-overlay">
                  <div className="loading-spinner"></div>
                  <p>جاري تحميل النموذج...</p>
                </div>
              )}
              
              <iframe
                src={formUrl}
                className="google-form-iframe"
                frameBorder="0"
                marginHeight="0"
                marginWidth="0"
                onLoad={() => setIsLoading(false)}
                title="Google Form للعقارات"
              >
                جاري التحميل...
              </iframe>
            </div>
          </div>
        )}

        <div className="form-footer">
          <div className="footer-info">
            <p>💡 <strong>نصيحة:</strong> يمكنك ملء النموذج مباشرة هنا أو فتحه في نافذة جديدة</p>
            <p>📊 البيانات ستُحفظ تلقائياً في Google Sheets المرتبط بالنموذج</p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default GoogleFormIntegration
