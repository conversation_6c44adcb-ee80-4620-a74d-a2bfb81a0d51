import { useState } from 'react'
import { formQuestions } from '../data/formQuestions'
import './TraditionalForm.css'

const TraditionalForm = ({ formData, setFormData, onSubmit }) => {
  const [currentStep, setCurrentStep] = useState(0)
  const [errors, setErrors] = useState({})

  const steps = [
    {
      title: 'أساسيات العقار',
      fields: ['unitType', 'unitCondition', 'region', 'floor']
    },
    {
      title: 'التفاصيل والمواصفات',
      fields: ['area', 'price', 'imageStatus', 'features', 'fullDetails', 'address']
    },
    {
      title: 'معلومات التتبع',
      fields: ['employeeName', 'propertyAvailability', 'status']
    }
  ]

  const currentStepFields = steps[currentStep].fields
  const progress = ((currentStep + 1) / steps.length) * 100

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
    
    // إزالة الخطأ عند التعديل
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: null
      }))
    }
  }

  const validateStep = () => {
    const newErrors = {}
    
    currentStepFields.forEach(field => {
      const question = formQuestions.find(q => q.field === field)
      if (question && question.required) {
        const value = formData[field]
        if (!value || (Array.isArray(value) && value.length === 0)) {
          newErrors[field] = 'هذا الحقل مطلوب'
        }
      }
    })

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleNext = () => {
    if (validateStep()) {
      if (currentStep < steps.length - 1) {
        setCurrentStep(currentStep + 1)
      } else {
        onSubmit(formData)
      }
    }
  }

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1)
    }
  }

  const renderField = (fieldName) => {
    const question = formQuestions.find(q => q.field === fieldName)
    if (!question) return null

    const value = formData[fieldName]
    const error = errors[fieldName]

    if (question.type === 'select') {
      return (
        <div key={fieldName} className="form-field">
          <label className="field-label">
            {question.question}
            {question.required && <span className="required">*</span>}
          </label>
          <select
            className={`field-input ${error ? 'error' : ''}`}
            value={value || ''}
            onChange={(e) => handleInputChange(fieldName, e.target.value)}
          >
            <option value="">اختر...</option>
            {question.options.map((option, index) => (
              <option key={index} value={option}>{option}</option>
            ))}
          </select>
          {error && <span className="error-message">{error}</span>}
        </div>
      )
    }

    if (question.type === 'multi-select') {
      return (
        <div key={fieldName} className="form-field">
          <label className="field-label">
            {question.question}
            {question.required && <span className="required">*</span>}
          </label>
          <div className="multi-select-options">
            {question.options.map((option, index) => (
              <label key={index} className="checkbox-label">
                <input
                  type="checkbox"
                  checked={(value || []).includes(option)}
                  onChange={(e) => {
                    const currentValues = value || []
                    const newValues = e.target.checked
                      ? [...currentValues, option]
                      : currentValues.filter(v => v !== option)
                    handleInputChange(fieldName, newValues)
                  }}
                />
                <span className="checkbox-text">{option}</span>
              </label>
            ))}
          </div>
          {error && <span className="error-message">{error}</span>}
        </div>
      )
    }

    if (question.type === 'number' || question.type === 'text') {
      return (
        <div key={fieldName} className="form-field">
          <label className="field-label">
            {question.question}
            {question.required && <span className="required">*</span>}
          </label>
          <div className="input-container">
            <input
              type={question.type}
              className={`field-input ${error ? 'error' : ''}`}
              value={value || ''}
              placeholder={question.placeholder || `أدخل ${question.question}`}
              onChange={(e) => handleInputChange(fieldName, e.target.value)}
            />
            {question.icon && <span className="input-icon">{question.icon}</span>}
          </div>
          {error && <span className="error-message">{error}</span>}
        </div>
      )
    }
  }

  return (
    <div className="traditional-form">
      <div className="form-progress">
        <div className="progress-bar">
          <div className="progress-fill" style={{ width: `${progress}%` }}></div>
        </div>
        <div className="step-indicators">
          {steps.map((step, index) => (
            <div 
              key={index} 
              className={`step-indicator ${index <= currentStep ? 'active' : ''}`}
            >
              <span className="step-number">{index + 1}</span>
              <span className="step-title">{step.title}</span>
            </div>
          ))}
        </div>
      </div>

      <div className="form-content">
        <div className="step-header">
          <h2 className="step-title">{steps[currentStep].title}</h2>
          <p className="step-description">
            الخطوة {currentStep + 1} من {steps.length}
          </p>
        </div>

        <div className="form-fields">
          {currentStepFields.map(fieldName => renderField(fieldName))}
        </div>

        <div className="form-actions">
          <div className="action-left">
            {currentStep > 0 && (
              <button
                type="button"
                className="btn btn-secondary"
                onClick={handlePrevious}
              >
                السابق
              </button>
            )}
          </div>

          <div className="action-center">
            <button
              type="button"
              className="btn btn-outline"
              onClick={() => {
                localStorage.setItem('aqar-form-draft', JSON.stringify({
                  ...formData,
                  currentStep,
                  savedAt: new Date().toISOString()
                }))
                alert('تم حفظ المسودة بنجاح!')
              }}
            >
              💾 حفظ كمسودة
            </button>
          </div>

          <div className="action-right">
            <button
              type="button"
              className="btn btn-primary"
              onClick={handleNext}
            >
              {currentStep === steps.length - 1 ? 'حفظ العقار' : 'التالي'}
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default TraditionalForm
