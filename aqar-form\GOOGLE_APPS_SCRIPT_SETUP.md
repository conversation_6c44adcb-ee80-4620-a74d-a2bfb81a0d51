# إعداد Google Apps Script لاستقبال البيانات

## الخطوة 1: إنشاء مشروع Google Apps Script

1. اذهب إلى [Google Apps Script](https://script.google.com)
2. انقر على "مشروع جديد"
3. اكتب اسم المشروع: "Aqar Data Receiver"

## الخطوة 2: كتابة الكود

استبدل الكود الافتراضي بالكود التالي:

```javascript
/**
 * استقبال البيانات من تطبيق العقارات وإضافتها إلى Google Sheets
 */

// معرف الجدول (استبدله بمعرف جدولك)
const SPREADSHEET_ID = '1g7empopPTGkd5ZiET18FMBi8rd72vzdlH_mdhbznDNw';
const SHEET_NAME = 'بيانات العقارات';

/**
 * معالج طلبات POST
 */
function doPost(e) {
  try {
    // قراءة البيانات من الطلب
    const data = JSON.parse(e.postData.contents);
    
    // فتح الجدول
    const spreadsheet = SpreadsheetApp.openById(SPREADSHEET_ID);
    let sheet = spreadsheet.getSheetByName(SHEET_NAME);
    
    // إنشاء الورقة إذا لم تكن موجودة
    if (!sheet) {
      sheet = spreadsheet.insertSheet(SHEET_NAME);
      
      // إضافة رؤوس الأعمدة
      const headers = [
        'كود العقار', 'نوع الوحدة', 'حالة الوحدة', 'المنطقة', 'الدور',
        'المساحة', 'السعر', 'حالة الصور', 'المميزات', 'التفاصيل الكاملة',
        'العنوان', 'اسم الموظف', 'إتاحة العقار', 'الحالة', 'تاريخ الإرسال',
        'تاريخ الإنشاء', 'آخر معالجة', 'تاريخ التذكير'
      ];
      sheet.getRange(1, 1, 1, headers.length).setValues([headers]);
      
      // تنسيق الرؤوس
      const headerRange = sheet.getRange(1, 1, 1, headers.length);
      headerRange.setFontWeight('bold');
      headerRange.setBackground('#4285f4');
      headerRange.setFontColor('white');
    }
    
    // إضافة البيانات
    if (data.data && data.data.length > 0) {
      const lastRow = sheet.getLastRow();
      const range = sheet.getRange(lastRow + 1, 1, data.data.length, data.data[0].length);
      range.setValues(data.data);
      
      // تنسيق البيانات الجديدة
      range.setBorder(true, true, true, true, true, true);
      
      // تلوين الصفوف بالتناوب
      for (let i = 0; i < data.data.length; i++) {
        const rowRange = sheet.getRange(lastRow + 1 + i, 1, 1, data.data[0].length);
        if ((lastRow + i) % 2 === 0) {
          rowRange.setBackground('#f8f9fa');
        }
      }
    }
    
    // ضبط عرض الأعمدة تلقائياً
    sheet.autoResizeColumns(1, sheet.getLastColumn());
    
    // إرجاع نتيجة النجاح
    return ContentService
      .createTextOutput(JSON.stringify({
        status: 'success',
        message: 'تم إضافة البيانات بنجاح',
        rowsAdded: data.data ? data.data.length : 0,
        timestamp: new Date().toISOString()
      }))
      .setMimeType(ContentService.MimeType.JSON);
      
  } catch (error) {
    // إرجاع رسالة خطأ
    return ContentService
      .createTextOutput(JSON.stringify({
        status: 'error',
        message: error.toString(),
        timestamp: new Date().toISOString()
      }))
      .setMimeType(ContentService.MimeType.JSON);
  }
}

/**
 * معالج طلبات GET (للاختبار)
 */
function doGet(e) {
  return ContentService
    .createTextOutput(JSON.stringify({
      status: 'success',
      message: 'خدمة استقبال بيانات العقارات تعمل بنجاح',
      timestamp: new Date().toISOString()
    }))
    .setMimeType(ContentService.MimeType.JSON);
}

/**
 * اختبار الاتصال مع الجدول
 */
function testConnection() {
  try {
    const spreadsheet = SpreadsheetApp.openById(SPREADSHEET_ID);
    console.log('تم الاتصال بالجدول بنجاح:', spreadsheet.getName());
    return true;
  } catch (error) {
    console.error('فشل في الاتصال بالجدول:', error);
    return false;
  }
}

/**
 * إنشاء بيانات تجريبية للاختبار
 */
function createTestData() {
  const testData = {
    data: [
      [
        'TEST001', 'شقة', 'مفروش', 'التجمع الخامس', 'دور ثالث',
        '120', '500000', 'بصور', 'شقة مميزة', 'شقة 3 غرف وصالة',
        'التجمع الخامس، القاهرة الجديدة', 'محمد نبيل', 'متاح', 'عقار ناجح',
        new Date().toISOString(), new Date().toISOString(), '', ''
      ]
    ]
  };
  
  // محاكاة طلب POST
  const mockEvent = {
    postData: {
      contents: JSON.stringify(testData)
    }
  };
  
  return doPost(mockEvent);
}
```

## الخطوة 3: نشر المشروع

1. انقر على "نشر" > "نشر كتطبيق ويب"
2. في "تنفيذ التطبيق كـ": اختر "أنا"
3. في "من لديه حق الوصول": اختر "أي شخص"
4. انقر على "نشر"
5. انسخ رابط تطبيق الويب

## الخطوة 4: تحديث الخدمة في التطبيق

1. افتح ملف `src/services/googleSheetsService.js`
2. استبدل `YOUR_SCRIPT_ID` برابط تطبيق الويب الذي نسخته
3. احفظ الملف

## الخطوة 5: اختبار النظام

1. في Google Apps Script، انقر على "تشغيل" > `createTestData`
2. تحقق من إضافة البيانات التجريبية إلى الجدول
3. اختبر الإرسال من التطبيق

## ملاحظات مهمة

- تأكد من أن معرف الجدول صحيح
- تأكد من أن لديك صلاحيات التحرير على الجدول
- قد تحتاج لإعادة نشر المشروع عند تعديل الكود
- احفظ رابط تطبيق الويب في مكان آمن

## استكشاف الأخطاء

### خطأ في الصلاحيات
- تأكد من أن المشروع منشور بصلاحيات "أي شخص"
- تحقق من صلاحيات الجدول

### خطأ في البيانات
- تحقق من تنسيق JSON المرسل
- تأكد من أن البيانات تحتوي على الحقول المطلوبة

### خطأ في الجدول
- تحقق من معرف الجدول
- تأكد من وجود الجدول وإمكانية الوصول إليه
