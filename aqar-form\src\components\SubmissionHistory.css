.submission-history-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
  backdrop-filter: blur(8px);
}

.submission-history-modal {
  background: white;
  border-radius: 20px;
  width: 100%;
  max-width: 1400px;
  max-height: 95vh;
  overflow: hidden;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
  animation: modalSlideIn 0.4s ease;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-header {
  background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
  color: white;
  padding: 25px 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h2 {
  margin: 0;
  font-size: 24px;
  font-weight: 700;
}

.close-btn {
  background: none;
  border: none;
  color: white;
  font-size: 30px;
  cursor: pointer;
  padding: 0;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: rotate(90deg);
}

.history-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  padding: 25px 30px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.stat-card {
  background: white;
  padding: 20px;
  border-radius: 12px;
  text-align: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-number {
  display: block;
  font-size: 32px;
  font-weight: 700;
  color: #6f42c1;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #6c757d;
  font-weight: 600;
}

.history-controls {
  padding: 20px 30px;
  border-bottom: 1px solid #e9ecef;
  text-align: center;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 25px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn:not(:disabled):hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.btn-danger {
  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
  color: white;
}

.btn-secondary {
  background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
  color: white;
}

.btn-sm {
  padding: 6px 12px;
  font-size: 12px;
}

.history-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  height: 60vh;
}

.submissions-list {
  border-right: 1px solid #e9ecef;
  overflow-y: auto;
  padding: 20px;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #6c757d;
  font-size: 18px;
}

.submission-card {
  background: #f8f9fa;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  animation: fadeInUp 0.5s ease;
}

.submission-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: #6f42c1;
}

.submission-card.selected {
  background: linear-gradient(135deg, #6f42c110 0%, #e83e8c10 100%);
  border-color: #6f42c1;
  box-shadow: 0 4px 20px rgba(111, 66, 193, 0.2);
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.submission-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.submission-info h3 {
  margin: 0 0 5px 0;
  color: #6f42c1;
  font-size: 18px;
  font-weight: 700;
}

.submission-date {
  margin: 0;
  color: #6c757d;
  font-size: 14px;
}

.status-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
}

.status-badge.success {
  background: #d4edda;
  color: #155724;
}

.status-badge.error {
  background: #f8d7da;
  color: #721c24;
}

.submission-summary {
  margin-bottom: 15px;
}

.properties-count {
  background: #e9ecef;
  color: #495057;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 600;
}

.submission-actions {
  display: flex;
  gap: 10px;
}

.submission-details {
  padding: 20px;
  overflow-y: auto;
  background: #f8f9fa;
}

.details-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 2px solid #e9ecef;
}

.details-header h3 {
  margin: 0;
  color: #6f42c1;
  font-size: 20px;
  font-weight: 700;
}

.close-details-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #6c757d;
  padding: 5px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.close-details-btn:hover {
  background: #e9ecef;
  color: #495057;
}

.details-info {
  margin-bottom: 25px;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #e9ecef;
}

.info-row .label {
  font-weight: 600;
  color: #495057;
}

.info-row .value {
  color: #6c757d;
}

.value.status-success {
  color: #28a745;
  font-weight: 600;
}

.value.status-error {
  color: #dc3545;
  font-weight: 600;
}

.properties-details h4 {
  color: #495057;
  margin-bottom: 15px;
  font-size: 16px;
}

.properties-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 10px;
}

.property-item {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 12px;
  text-align: center;
}

.property-code {
  font-weight: 700;
  color: #6f42c1;
  font-size: 14px;
  margin-bottom: 5px;
}

.property-type {
  color: #6c757d;
  font-size: 12px;
  margin-bottom: 5px;
}

.property-price {
  color: #28a745;
  font-weight: 600;
  font-size: 12px;
}

/* تحسينات للهواتف */
@media (max-width: 768px) {
  .submission-history-overlay {
    padding: 10px;
  }
  
  .submission-history-modal {
    max-height: 98vh;
    border-radius: 15px;
  }
  
  .modal-header {
    padding: 20px;
  }
  
  .modal-header h2 {
    font-size: 20px;
  }
  
  .history-stats {
    grid-template-columns: 1fr;
    gap: 15px;
    padding: 20px;
  }
  
  .history-content {
    grid-template-columns: 1fr;
    height: auto;
  }
  
  .submissions-list {
    border-right: none;
    border-bottom: 1px solid #e9ecef;
    max-height: 40vh;
  }
  
  .submission-details {
    max-height: 40vh;
  }
  
  .submission-actions {
    flex-direction: column;
  }
  
  .properties-grid {
    grid-template-columns: 1fr;
  }
}

/* تأثيرات إضافية */
.submissions-list::-webkit-scrollbar,
.submission-details::-webkit-scrollbar {
  width: 8px;
}

.submissions-list::-webkit-scrollbar-track,
.submission-details::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.submissions-list::-webkit-scrollbar-thumb,
.submission-details::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.submissions-list::-webkit-scrollbar-thumb:hover,
.submission-details::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
