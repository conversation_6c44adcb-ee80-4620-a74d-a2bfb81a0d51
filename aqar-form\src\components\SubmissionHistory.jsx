import { useState, useEffect } from 'react'
import './SubmissionHistory.css'

const SubmissionHistory = ({ onClose }) => {
  const [submissionHistory, setSubmissionHistory] = useState([])
  const [selectedSubmission, setSelectedSubmission] = useState(null)

  useEffect(() => {
    const history = JSON.parse(localStorage.getItem('submission-history') || '[]')
    setSubmissionHistory(history)
  }, [])

  const formatDate = (dateString) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('ar-EG', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const exportSubmissionData = (submission) => {
    const headers = [
      'كود العقار', 'نوع الوحدة', 'حالة الوحدة', 'المنطقة', 'الدور',
      'المساحة', 'السعر', 'حالة الصور', 'المميزات', 'التفاصيل الكاملة',
      'العنوان', 'اسم الموظف', 'إتاحة العقار', 'الحالة', 'تاريخ الإنشاء'
    ]

    const csvContent = [
      headers.join(','),
      ...submission.properties.map(property => [
        property.unitCode || '',
        property.unitType || '',
        property.unitCondition || '',
        property.region || '',
        property.floor || '',
        property.area || '',
        property.price || '',
        property.imageStatus || '',
        property.features || '',
        property.fullDetails || '',
        property.address || '',
        property.employeeName || '',
        property.propertyAvailability || '',
        property.status || '',
        property.createdAt ? new Date(property.createdAt).toLocaleDateString('ar-EG') : ''
      ].map(field => `"${field}"`).join(','))
    ].join('\n')

    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    link.href = URL.createObjectURL(blob)
    link.download = `إرسال_${submission.id}_${new Date(submission.date).toISOString().split('T')[0]}.csv`
    link.click()
  }

  const deleteSubmission = (submissionId) => {
    if (confirm('هل أنت متأكد من حذف هذا السجل؟')) {
      const updatedHistory = submissionHistory.filter(s => s.id !== submissionId)
      setSubmissionHistory(updatedHistory)
      localStorage.setItem('submission-history', JSON.stringify(updatedHistory))
      if (selectedSubmission && selectedSubmission.id === submissionId) {
        setSelectedSubmission(null)
      }
    }
  }

  const clearAllHistory = () => {
    if (confirm('هل أنت متأكد من حذف جميع سجلات الإرسال؟ هذا الإجراء لا يمكن التراجع عنه.')) {
      setSubmissionHistory([])
      setSelectedSubmission(null)
      localStorage.removeItem('submission-history')
    }
  }

  return (
    <div className="submission-history-overlay">
      <div className="submission-history-modal">
        <div className="modal-header">
          <h2>تاريخ إرسال البيانات</h2>
          <button className="close-btn" onClick={onClose}>×</button>
        </div>

        <div className="history-stats">
          <div className="stat-card">
            <span className="stat-number">{submissionHistory.length}</span>
            <span className="stat-label">عملية إرسال</span>
          </div>
          <div className="stat-card">
            <span className="stat-number">
              {submissionHistory.reduce((total, submission) => total + submission.propertiesCount, 0)}
            </span>
            <span className="stat-label">عقار تم إرساله</span>
          </div>
          <div className="stat-card">
            <span className="stat-number">
              {submissionHistory.filter(s => s.status === 'success').length}
            </span>
            <span className="stat-label">إرسال ناجح</span>
          </div>
        </div>

        <div className="history-controls">
          <button 
            className="btn btn-danger"
            onClick={clearAllHistory}
            disabled={submissionHistory.length === 0}
          >
            🗑️ مسح جميع السجلات
          </button>
        </div>

        <div className="history-content">
          <div className="submissions-list">
            {submissionHistory.length === 0 ? (
              <div className="empty-state">
                لا توجد عمليات إرسال سابقة
              </div>
            ) : (
              submissionHistory.map((submission) => (
                <div 
                  key={submission.id} 
                  className={`submission-card ${selectedSubmission?.id === submission.id ? 'selected' : ''}`}
                  onClick={() => setSelectedSubmission(submission)}
                >
                  <div className="submission-header">
                    <div className="submission-info">
                      <h3>إرسال #{submission.id}</h3>
                      <p className="submission-date">{formatDate(submission.date)}</p>
                    </div>
                    <div className="submission-badge">
                      <span className={`status-badge ${submission.status}`}>
                        {submission.status === 'success' ? '✅ نجح' : '❌ فشل'}
                      </span>
                    </div>
                  </div>
                  
                  <div className="submission-summary">
                    <span className="properties-count">
                      {submission.propertiesCount} عقار
                    </span>
                  </div>
                  
                  <div className="submission-actions">
                    <button 
                      className="btn btn-sm btn-secondary"
                      onClick={(e) => {
                        e.stopPropagation()
                        exportSubmissionData(submission)
                      }}
                    >
                      📊 تصدير
                    </button>
                    <button 
                      className="btn btn-sm btn-danger"
                      onClick={(e) => {
                        e.stopPropagation()
                        deleteSubmission(submission.id)
                      }}
                    >
                      🗑️ حذف
                    </button>
                  </div>
                </div>
              ))
            )}
          </div>

          {selectedSubmission && (
            <div className="submission-details">
              <div className="details-header">
                <h3>تفاصيل الإرسال #{selectedSubmission.id}</h3>
                <button 
                  className="close-details-btn"
                  onClick={() => setSelectedSubmission(null)}
                >
                  ×
                </button>
              </div>
              
              <div className="details-info">
                <div className="info-row">
                  <span className="label">تاريخ الإرسال:</span>
                  <span className="value">{formatDate(selectedSubmission.date)}</span>
                </div>
                <div className="info-row">
                  <span className="label">عدد العقارات:</span>
                  <span className="value">{selectedSubmission.propertiesCount}</span>
                </div>
                <div className="info-row">
                  <span className="label">الحالة:</span>
                  <span className={`value status-${selectedSubmission.status}`}>
                    {selectedSubmission.status === 'success' ? 'نجح الإرسال' : 'فشل الإرسال'}
                  </span>
                </div>
              </div>

              <div className="properties-details">
                <h4>العقارات المرسلة:</h4>
                <div className="properties-grid">
                  {selectedSubmission.properties.map((property, index) => (
                    <div key={index} className="property-item">
                      <div className="property-code">{property.unitCode}</div>
                      <div className="property-type">{property.unitType}</div>
                      <div className="property-price">{property.price} جنيه</div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default SubmissionHistory
