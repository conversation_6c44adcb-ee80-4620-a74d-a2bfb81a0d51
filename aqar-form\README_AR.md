# نموذج العقارات المتطور 🏠

نموذج تفاعلي متطور لإدخال بيانات العقارات يدعم طريقتين للإدخال: النموذج الحواري والنموذج التقليدي.

## ✨ المميزات

### 🎯 طريقتان للإدخال
- **النموذج الحواري**: تجربة تفاعلية تشبه المحادثة، سؤال واحد في كل مرة
- **النموذج التقليدي**: نموذج متعدد المراحل مع جميع الحقول منظمة في خطوات

### 📱 تصميم متجاوب
- محسّن للهواتف الذكية والأجهزة اللوحية
- تصميم عصري مع تدرجات لونية جذابة
- تأثيرات حركية ناعمة وتفاعلية

### 💾 حفظ البيانات
- حفظ تلقائي للبيانات محلياً
- إمكانية حفظ المسودات
- عرض العقارات المحفوظة مع إمكانية البحث
- تصدير البيانات إلى ملف CSV

### 🔧 مميزات متقدمة
- التحقق من صحة البيانات
- دعم الاختيار المتعدد
- شريط تقدم مرئي
- رسائل تأكيد وتنبيهات
- أيقونات تعبيرية لكل حقل

## 🚀 التشغيل

### متطلبات النظام
- Node.js (الإصدار 16 أو أحدث)
- npm أو yarn

### خطوات التشغيل

1. **تثبيت التبعيات**
   ```bash
   npm install
   ```

2. **تشغيل المشروع**
   ```bash
   npm run dev
   ```

3. **فتح المتصفح**
   انتقل إلى `http://localhost:5173`

## 📋 حقول النموذج

### أساسيات العقار
- **نوع الوحدة**: مكتب، شقة، فيلا، إلخ
- **حالة الوحدة**: مفروش، فاضي، تمليك، إلخ
- **المنطقة**: قائمة شاملة بالمناطق المتاحة
- **الدور**: من البيزمنت إلى الدور الثامن

### التفاصيل والمواصفات
- **المساحة**: بالمتر المربع
- **السعر**: بالجنيه المصري
- **حالة الصور**: بصور أو بدون صور
- **المميزات**: وصف المميزات الخاصة
- **التفاصيل الكاملة**: وصف مفصل
- **العنوان**: العنوان التفصيلي

### معلومات التتبع
- **اسم الموظف**: الموظف المسؤول
- **إتاحة العقار**: متاح، مؤجر، إلخ
- **الحالة**: عقار ناجح، فاشل، إلخ

## 🎨 التصميم

### الألوان الرئيسية
- **التدرج الأساسي**: من `#667eea` إلى `#764ba2`
- **اللون الثانوي**: `#28a745` للأزرار الإيجابية
- **الخلفية**: تدرج أزرق-بنفسجي

### الخطوط
- **الخط الأساسي**: Segoe UI, Tahoma, Geneva, Verdana, sans-serif
- **اتجاه النص**: من اليمين إلى اليسار (RTL)

## 📱 التوافق

### المتصفحات المدعومة
- Chrome (الإصدار 80+)
- Firefox (الإصدار 75+)
- Safari (الإصدار 13+)
- Edge (الإصدار 80+)

### الأجهزة
- أجهزة الكمبيوتر المكتبية
- الأجهزة اللوحية
- الهواتف الذكية (iOS و Android)

## 🔧 التخصيص

### إضافة حقول جديدة
1. افتح ملف `src/data/formQuestions.js`
2. أضف الحقل الجديد إلى مصفوفة `formQuestions`
3. حدد نوع الحقل (`select`, `multi-select`, `text`, `number`)
4. أضف الخيارات إذا كان الحقل من نوع اختيار

### تعديل التصميم
- **الألوان**: عدّل متغيرات CSS في ملفات `.css`
- **الخطوط**: غيّر `font-family` في `App.css`
- **التخطيط**: عدّل Grid و Flexbox في ملفات المكونات

## 📊 تصدير البيانات

يمكن تصدير العقارات المحفوظة إلى ملف CSV يحتوي على:
- كود العقار
- نوع الوحدة
- المنطقة
- المساحة
- السعر
- الموظف المسؤول
- تاريخ الإنشاء

## 🔒 الأمان

- جميع البيانات محفوظة محلياً في المتصفح
- لا يتم إرسال البيانات إلى خوادم خارجية
- يمكن مسح البيانات المحفوظة من إعدادات المتصفح

## 🛠️ التطوير

### بنية المشروع
```
src/
├── components/          # مكونات React
│   ├── AqarForm.jsx    # المكون الرئيسي
│   ├── ConversationalForm.jsx  # النموذج الحواري
│   ├── TraditionalForm.jsx     # النموذج التقليدي
│   └── SavedProperties.jsx     # عرض العقارات المحفوظة
├── data/               # بيانات النموذج
│   └── formQuestions.js # تعريف الأسئلة والحقول
└── styles/             # ملفات CSS
```

### إضافة مميزات جديدة
1. أنشئ مكون جديد في مجلد `components`
2. أضف ملف CSS مقابل للتصميم
3. استورد المكون في `AqarForm.jsx`
4. اختبر على جميع أحجام الشاشات

## 📞 الدعم

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- افتح issue جديد في المستودع
- تأكد من تضمين تفاصيل المشكلة
- أرفق لقطات شاشة إذا أمكن

## 📄 الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام والتعديل.

---

**تم تطويره بـ ❤️ لتسهيل إدارة العقارات**
