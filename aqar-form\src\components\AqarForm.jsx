import { useState, useEffect } from 'react'
import ConversationalForm from './ConversationalForm'
import TraditionalForm from './TraditionalForm'
import SavedProperties from './SavedProperties'
import DataSubmission from './DataSubmission'
import GoogleFormIntegration from './GoogleFormIntegration'
import './AqarForm.css'

const AqarForm = () => {
  const [formMode, setFormMode] = useState('conversational') // 'conversational' or 'traditional'
  const [showSavedProperties, setShowSavedProperties] = useState(false)
  const [showDataSubmission, setShowDataSubmission] = useState(false)
  const [showGoogleForm, setShowGoogleForm] = useState(false)
  const [formData, setFormData] = useState({
    unitType: '',
    unitCondition: [],
    area: '',
    floor: [],
    price: '',
    imageStatus: '',
    features: '',
    fullDetails: '',
    address: '',
    employeeName: '',
    propertyAvailability: '',
    status: [],
    region: []
  })

  // حفظ البيانات محلياً
  useEffect(() => {
    const savedData = localStorage.getItem('aqar-form-data')
    const savedDraft = localStorage.getItem('aqar-form-draft')

    if (savedDraft) {
      const shouldLoadDraft = confirm('تم العثور على مسودة محفوظة. هل تريد تحميلها؟')
      if (shouldLoadDraft) {
        try {
          const draftData = JSON.parse(savedDraft)
          setFormData(draftData)
          localStorage.removeItem('aqar-form-draft') // إزالة المسودة بعد التحميل
        } catch (error) {
          console.error('خطأ في تحميل المسودة:', error)
        }
      }
    } else if (savedData) {
      try {
        setFormData(JSON.parse(savedData))
      } catch (error) {
        console.error('خطأ في تحميل البيانات المحفوظة:', error)
      }
    }
  }, [])

  useEffect(() => {
    localStorage.setItem('aqar-form-data', JSON.stringify(formData))
  }, [formData])

  const toggleFormMode = () => {
    setFormMode(formMode === 'conversational' ? 'traditional' : 'conversational')
  }

  const handleFormSubmit = (data) => {
    // إضافة تاريخ الإنشاء والمعالجة
    const submissionData = {
      ...data,
      createdAt: new Date().toISOString(),
      lastProcessed: new Date().toISOString(),
      unitCode: `AQAR-${Date.now()}`, // كود فريد للوحدة
      reminderDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString() // تذكير بعد أسبوع
    }

    console.log('Form submitted:', submissionData)

    // حفظ البيانات في localStorage كنسخة احتياطية
    const savedProperties = JSON.parse(localStorage.getItem('saved-properties') || '[]')
    savedProperties.push(submissionData)
    localStorage.setItem('saved-properties', JSON.stringify(savedProperties))

    // مسح بيانات الفورم الحالي
    localStorage.removeItem('aqar-form-data')

    // هنا يمكن إرسال البيانات إلى الخادم
    // await fetch('/api/properties', { method: 'POST', body: JSON.stringify(submissionData) })

    alert(`تم حفظ بيانات العقار بنجاح!\nكود العقار: ${submissionData.unitCode}`)

    // إعادة تعيين الفورم
    setFormData({
      unitType: '',
      unitCondition: [],
      area: '',
      floor: [],
      price: '',
      imageStatus: '',
      features: '',
      fullDetails: '',
      address: '',
      employeeName: '',
      propertyAvailability: '',
      status: [],
      region: []
    })
  }

  return (
    <div className="aqar-form-container">
      <div className="form-header">
        <h1 className="form-title">إضافة عقار جديد</h1>

        <div className="header-controls">
          <button
            className="saved-properties-btn"
            onClick={() => setShowSavedProperties(true)}
            title="عرض العقارات المحفوظة"
          >
            📁 العقارات المحفوظة
          </button>

          <button
            className="data-submission-btn"
            onClick={() => setShowDataSubmission(true)}
            title="إرسال البيانات الأصلية"
          >
            📤 إرسال البيانات
          </button>

          <button
            className="google-form-btn"
            onClick={() => setShowGoogleForm(true)}
            title="استخدام Google Form"
          >
            📝 Google Form
          </button>

          <button
            className="mode-toggle-btn"
            onClick={toggleFormMode}
            title={formMode === 'conversational' ? 'التبديل للنموذج التقليدي' : 'التبديل للنموذج الحواري'}
          >
            {formMode === 'conversational' ? (
              <span>📋 النموذج التقليدي</span>
            ) : (
              <span>💬 النموذج الحواري</span>
            )}
          </button>
        </div>
      </div>

      {formMode === 'conversational' ? (
        <ConversationalForm
          formData={formData}
          setFormData={setFormData}
          onSubmit={handleFormSubmit}
        />
      ) : (
        <TraditionalForm
          formData={formData}
          setFormData={setFormData}
          onSubmit={handleFormSubmit}
        />
      )}

      {showSavedProperties && (
        <SavedProperties onClose={() => setShowSavedProperties(false)} />
      )}

      {showDataSubmission && (
        <DataSubmission onClose={() => setShowDataSubmission(false)} />
      )}

      {showGoogleForm && (
        <GoogleFormIntegration onClose={() => setShowGoogleForm(false)} />
      )}
    </div>
  )
}

export default AqarForm
