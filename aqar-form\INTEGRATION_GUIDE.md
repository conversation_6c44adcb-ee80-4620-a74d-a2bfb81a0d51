# دليل دمج Google Forms مع Google Sheets

## نظرة عامة

تم تطوير النظام ليدعم ثلاث طرق لإدارة بيانات العقارات:

1. **الحفظ المحلي**: حفظ البيانات في متصفح المستخدم
2. **Google Sheets**: إرسال البيانات مباشرة إلى جدول Google Sheets
3. **Google Form**: استخدام نموذج Google Form مدمج

## الإعداد الكامل

### الخطوة 1: إنشاء Google Sheets

1. اذهب إلى [Google Sheets](https://sheets.google.com)
2. أنشئ جدول جديد باسم "بيانات العقارات"
3. انسخ معرف الجدول من الرابط
4. <PERSON><PERSON><PERSON> المعرف إلى ملف `.env`:
   ```
   GOOGLE_SHEETS_ID=your_sheets_id_here
   ```

### الخطوة 2: إنشاء Google Form

1. اتبع التعليمات في ملف `GOOGLE_FORM_SETUP_GUIDE.md`
2. أنشئ النموذج مع جميع الحقول المطلوبة
3. اربط النموذج بـ Google Sheets
4. انسخ معرف النموذج وأضفه إلى ملف `.env`:
   ```
   GOOGLE_FORM_ID=your_form_id_here
   ```

### الخطوة 3: إعداد Google Apps Script

1. اتبع التعليمات في ملف `GOOGLE_APPS_SCRIPT_SETUP.md`
2. أنشئ مشروع Google Apps Script
3. انسخ الكود المطلوب
4. انشر المشروع كتطبيق ويب
5. أضف رابط التطبيق إلى ملف `.env`:
   ```
   GOOGLE_APPS_SCRIPT_URL=your_script_url_here
   ```

### الخطوة 4: تحديث خدمة Google Sheets

1. افتح ملف `src/services/googleSheetsService.js`
2. استبدل `YOUR_SCRIPT_ID` برابط Google Apps Script الفعلي
3. احفظ الملف

## كيفية الاستخدام

### 1. الحفظ المحلي

- الطريقة الافتراضية
- البيانات تُحفظ في متصفح المستخدم
- لا تحتاج إعداد إضافي
- مناسبة للاختبار والاستخدام الشخصي

### 2. Google Sheets

1. افتح التطبيق
2. انقر على "📤 إرسال البيانات"
3. اختر "Google Sheets" كطريقة الإرسال
4. حدد العقارات المراد إرسالها
5. انقر على "إرسال"

**المميزات:**
- إرسال مباشر إلى Google Sheets
- إمكانية إرسال عدة عقارات مرة واحدة
- تتبع حالة الإرسال
- رابط مباشر لعرض الجدول

### 3. Google Form

1. افتح التطبيق
2. انقر على "📝 Google Form"
3. أدخل معرف النموذج (في المرة الأولى)
4. املأ النموذج مباشرة في التطبيق
5. أو افتحه في نافذة جديدة

**المميزات:**
- واجهة Google Form الأصلية
- حفظ تلقائي في Google Sheets
- إمكانية الوصول من أي مكان
- سهولة المشاركة مع الفريق

## استكشاف الأخطاء

### مشكلة: لا يعمل إرسال Google Sheets

**الحلول:**
1. تحقق من معرف الجدول في ملف `.env`
2. تأكد من نشر Google Apps Script بصلاحيات "أي شخص"
3. تحقق من رابط Google Apps Script في الخدمة
4. تأكد من صلاحيات التحرير على الجدول

### مشكلة: لا يظهر Google Form

**الحلول:**
1. تحقق من معرف النموذج
2. تأكد من أن النموذج منشور ومتاح للجميع
3. تحقق من إعدادات المتصفح (قد يحجب الإطارات المدمجة)
4. جرب فتح النموذج في نافذة جديدة

### مشكلة: خطأ في الاتصال

**الحلول:**
1. تحقق من اتصال الإنترنت
2. تأكد من أن Google Services متاحة
3. تحقق من إعدادات الحماية في المتصفح
4. جرب إعادة تحميل الصفحة

## الأمان والخصوصية

### البيانات المحلية
- تُحفظ في localStorage للمتصفح
- لا تُرسل إلى أي خادم خارجي
- يمكن مسحها من إعدادات المتصفح

### Google Sheets
- البيانات تُرسل إلى Google Sheets الخاص بك
- تحكم كامل في الصلاحيات
- يمكن تشفير الجدول أو جعله خاص

### Google Form
- يستخدم أمان Google الأصلي
- البيانات محمية بنفس مستوى أمان Google
- يمكن تحديد من يمكنه الوصول للنموذج

## النصائح والتوصيات

### للاستخدام الشخصي
- استخدم الحفظ المحلي للاختبار
- استخدم Google Form للإدخال السريع

### للفرق الصغيرة
- استخدم Google Form مع Google Sheets
- شارك رابط النموذج مع الفريق
- راقب البيانات من Google Sheets

### للمؤسسات
- استخدم Google Sheets API للتكامل المتقدم
- أضف طبقات أمان إضافية
- استخدم Google Workspace للتحكم الكامل

## الدعم الفني

### الموارد المفيدة
- [Google Forms Help](https://support.google.com/forms)
- [Google Sheets Help](https://support.google.com/sheets)
- [Google Apps Script Documentation](https://developers.google.com/apps-script)

### الملفات المرجعية
- `GOOGLE_FORM_SETUP_GUIDE.md`: دليل إنشاء Google Form
- `GOOGLE_APPS_SCRIPT_SETUP.md`: دليل إعداد Google Apps Script
- `README_AR.md`: دليل التطبيق الأساسي

## التحديثات المستقبلية

### المميزات المخططة
- دعم Google Drive API
- تصدير إلى PDF
- إشعارات البريد الإلكتروني
- تقارير تحليلية متقدمة
- دعم الصور والملفات المرفقة

### التحسينات المقترحة
- واجهة إدارة متقدمة
- نظام صلاحيات متعدد المستويات
- تكامل مع أنظمة CRM
- تطبيق جوال مخصص
